# موقع الزائرين - رحلات العمرة من الرياض

موقع احترافي لعرض رحلات العمرة من الرياض مبني بـ PHP و MySQL مع تصميم متجاوب ومتوافق مع السيو.

## المميزات

- ✅ تصميم احترافي ومتجاوب
- ✅ متوافق مع السيو (SEO Optimized)
- ✅ سريع وخفيف
- ✅ نظام قاعدة بيانات متقدم
- ✅ تفاعل عبر واتساب فقط
- ✅ عرض نوعين من الرحلات (اقتصادية و VIP)
- ✅ معرض صور للباصات والفنادق
- ✅ صفحات خطأ مخصصة
- ✅ ملف Sitemap تلقائي

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache مع mod_rewrite مفعل
- مساحة تخزين: 50MB على الأقل

## التثبيت

### 1. رفع الملفات
```bash
# رفع جميع الملفات إلى مجلد الموقع
# مثال: /var/www/html أو C:\xampp\htdocs\
```

### 2. إعداد قاعدة البيانات

#### الطريقة الأولى: تلقائياً
- افتح الموقع في المتصفح
- سيتم إنشاء قاعدة البيانات والجداول تلقائياً

#### الطريقة الثانية: يدوياً
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE umrah_trips CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الجداول
mysql -u root -p umrah_trips < database.sql
```

### 3. إعداد الصور
- ارفع صور الباصات والفنادق إلى مجلد `assets/images/`
- الصور المطلوبة:
  - `bus1.jpg` - باص مكيف
  - `bus2.jpg` - باص VIP
  - `hotel1.jpg` - فندق قريب من الحرم
  - `hotel2.jpg` - غرف فندقية
  - `about.jpg` - صورة قسم من نحن

### 4. تخصيص الإعدادات

#### تحديث رقم الواتساب
```php
// في ملف config/database.php أو مباشرة في قاعدة البيانات
UPDATE site_settings SET setting_value = '966501234567' WHERE setting_key = 'whatsapp_number';
```

#### تحديث معلومات الموقع
```sql
UPDATE site_settings SET setting_value = 'اسم شركتك' WHERE setting_key = 'site_name';
UPDATE site_settings SET setting_value = 'وصف شركتك' WHERE setting_key = 'site_description';
```

## هيكل الملفات

```
alzz/
├── index.php              # الصفحة الرئيسية
├── 404.php               # صفحة خطأ 404
├── sitemap.php           # خريطة الموقع
├── robots.txt            # ملف الروبوتات
├── .htaccess            # إعدادات Apache
├── database.sql         # هيكل قاعدة البيانات
├── config/
│   └── database.php     # إعدادات قاعدة البيانات
├── assets/
│   ├── css/
│   │   └── style.css    # ملف التصميم
│   ├── js/
│   │   └── script.js    # ملف JavaScript
│   └── images/          # مجلد الصور
└── README.md           # هذا الملف
```

## قاعدة البيانات

### الجداول الرئيسية

1. **trips** - جدول الرحلات
2. **gallery** - جدول معرض الصور
3. **site_settings** - جدول إعدادات الموقع
4. **inquiries** - جدول الاستفسارات (للمستقبل)

### Views المتاحة

- `active_trips` - عرض الرحلات النشطة
- `active_gallery` - عرض الصور النشطة

## التخصيص

### إضافة رحلة جديدة
```sql
INSERT INTO trips (type, duration, destination, schedule_days, description, features) 
VALUES ('economy', '7 أيام', 'مكة والمدينة وجدة', 'يومياً', 'رحلة شاملة', JSON_ARRAY('ميزة 1', 'ميزة 2'));
```

### إضافة صورة جديدة
```sql
INSERT INTO gallery (image_path, alt_text, category, title, description) 
VALUES ('assets/images/new_image.jpg', 'وصف الصورة', 'bus', 'عنوان الصورة', 'وصف مفصل');
```

### تحديث إعدادات الموقع
```sql
UPDATE site_settings SET setting_value = 'القيمة الجديدة' WHERE setting_key = 'المفتاح';
```

## الأمان

- ✅ حماية من SQL Injection
- ✅ تنظيف البيانات المدخلة
- ✅ حماية الملفات الحساسة
- ✅ Headers أمان إضافية
- ✅ منع الوصول للمجلدات الحساسة

## السيو (SEO)

- ✅ Meta tags محسنة
- ✅ Open Graph tags
- ✅ URLs نظيفة
- ✅ Sitemap تلقائي
- ✅ ملف robots.txt
- ✅ تحسين سرعة التحميل
- ✅ ضغط الملفات
- ✅ تخزين مؤقت للمتصفح

## الدعم الفني

### المشاكل الشائعة

1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من صحة بيانات الاتصال في `config/database.php`
   - تأكد من تشغيل MySQL

2. **الصور لا تظهر**
   - تأكد من رفع الصور إلى `assets/images/`
   - تأكد من صحة أسماء الملفات

3. **الموقع لا يعمل**
   - تأكد من تفعيل mod_rewrite في Apache
   - تأكد من صحة ملف .htaccess

### تحديث الموقع

1. عمل نسخة احتياطية من قاعدة البيانات
2. عمل نسخة احتياطية من الملفات
3. رفع الملفات الجديدة
4. تشغيل أي استعلامات تحديث مطلوبة

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله حسب الحاجة.

## التواصل

للدعم الفني أو الاستفسارات، يرجى التواصل عبر:
- واتساب: +966500000000 (غير هذا الرقم)

---

**ملاحظة**: تأكد من تحديث رقم الواتساب ومعلومات الشركة قبل تشغيل الموقع.
