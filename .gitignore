# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~

# PHP
vendor/
composer.lock
*.log

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment Files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.sql.backup
*.db
*.sqlite

# Logs
logs/
*.log
error.log
access.log

# Cache
cache/
tmp/
temp/

# Uploads
uploads/
user_uploads/

# Backups
backups/
*.backup
*.bak

# Configuration (if contains sensitive data)
# config/database.php (uncomment if you want to ignore)

# Build files
dist/
build/
*.min.css
*.min.js

# Package managers
package-lock.json
yarn.lock

# Testing
coverage/
.nyc_output/
.phpunit.result.cache

# Deployment
deploy.php
deploy.sh

# Temporary files
*.tmp
*.temp

# Security
.htpasswd
*.key
*.pem
*.crt

# Custom
setup.php
test.php
update_settings.php

# Images (uncomment if you don't want to track images)
# assets/images/*.jpg
# assets/images/*.jpeg
# assets/images/*.png
# assets/images/*.gif
# assets/images/*.webp

# Documentation build
docs/_build/

# Local development
local_config.php
dev_config.php
