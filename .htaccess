# Apache Configuration for Umrah Trips Website
# إعدادات Apache لموقع رحلات العمرة

# Enable Rewrite Engine
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (basic)
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self';"
    
    # Remove Server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Hide sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect config directory
<IfModule mod_rewrite.c>
    RewriteRule ^config/ - [F,L]
</IfModule>

# Compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Alternative caching using mod_headers
<IfModule mod_headers.c>
    # Cache images for 1 month
    <FilesMatch "\.(jpg|jpeg|png|gif|webp|svg|ico)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>
    
    # Cache CSS and JS for 1 month
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>
    
    # Cache fonts for 1 year
    <FilesMatch "\.(ttf|otf|woff|woff2)$">
        Header set Cache-Control "max-age=31536000, public"
    </FilesMatch>
    
    # Don't cache HTML files
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
</IfModule>

# URL Rewriting for SEO-friendly URLs
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Force HTTPS (uncomment if you have SSL)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Remove www (uncomment if you want to remove www)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
    
    # Add trailing slash to directories
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_URI} !(.*)/$
    RewriteRule ^(.*)$ $1/ [L,R=301]
    
    # Remove .php extension from URLs
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^([^\.]+)$ $1.php [NC,L]
    
    # Redirect .php extension to clean URLs
    RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
    RewriteRule ^ /%1 [NC,L,R=301]
    
    # Custom error pages
    ErrorDocument 404 /404.php
    ErrorDocument 500 /500.php
</IfModule>

# Prevent access to sensitive directories
<IfModule mod_rewrite.c>
    RewriteRule ^(config|logs|backups)/ - [F,L]
</IfModule>

# MIME Types
<IfModule mod_mime.c>
    # Web fonts
    AddType application/font-woff woff
    AddType application/font-woff2 woff2
    AddType application/vnd.ms-fontobject eot
    AddType application/x-font-ttf ttf
    AddType font/opentype otf
    
    # Images
    AddType image/webp webp
    AddType image/svg+xml svg
    
    # Other
    AddType application/json json
    AddType application/ld+json jsonld
</IfModule>

# Disable server signature
ServerSignature Off

# Prevent access to .htaccess itself
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|original|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Limit file upload size (if needed)
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
</IfModule>

# Prevent hotlinking of images
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [F]
</IfModule>

# Enable ETags
<IfModule mod_headers.c>
    Header unset ETag
</IfModule>
FileETag None

# Custom redirects (add as needed)
# Redirect 301 /old-page /new-page

# Sitemap location hint
# RewriteRule ^sitemap\.xml$ /sitemap.php [L]

# Robots.txt location hint
# RewriteRule ^robots\.txt$ /robots.php [L]
