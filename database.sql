-- إن<PERSON>ا<PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS umrah_trips CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE umrah_trips;

-- جدول الرحلات
CREATE TABLE IF NOT EXISTS trips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type ENUM('economy', 'vip') NOT NULL COMMENT 'نوع الرحلة: اقتصادية أو VIP',
    duration VARCHAR(20) NOT NULL COMMENT 'مدة الرحلة',
    destination VARCHAR(100) NOT NULL COMMENT 'الوجهة',
    schedule_days VARCHAR(100) NOT NULL COMMENT 'أيام الانطلاق',
    description TEXT COMMENT 'وصف الرحلة',
    features JSON COMMENT 'مميزات الرحلة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الرحلة متاحة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول معرض الصور
CREATE TABLE IF NOT EXISTS gallery (
    id INT AUTO_INCREMENT PRIMARY KEY,
    image_path VARCHAR(255) NOT NULL COMMENT 'مسار الصورة',
    alt_text VARCHAR(255) COMMENT 'النص البديل للصورة',
    category ENUM('bus', 'hotel', 'general') NOT NULL COMMENT 'فئة الصورة',
    title VARCHAR(100) COMMENT 'عنوان الصورة',
    description TEXT COMMENT 'وصف الصورة',
    sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الصورة مفعلة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إعدادات الموقع
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL COMMENT 'مفتاح الإعداد',
    setting_value TEXT COMMENT 'قيمة الإعداد',
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text' COMMENT 'نوع الإعداد',
    description VARCHAR(255) COMMENT 'وصف الإعداد',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الاستفسارات (اختياري للمستقبل)
CREATE TABLE IF NOT EXISTS inquiries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'اسم المستفسر',
    phone VARCHAR(20) NOT NULL COMMENT 'رقم الهاتف',
    trip_type ENUM('economy', 'vip') COMMENT 'نوع الرحلة المطلوبة',
    message TEXT COMMENT 'رسالة الاستفسار',
    status ENUM('new', 'contacted', 'closed') DEFAULT 'new' COMMENT 'حالة الاستفسار',
    source VARCHAR(50) DEFAULT 'website' COMMENT 'مصدر الاستفسار',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج البيانات الأساسية للرحلات
INSERT INTO trips (type, duration, destination, schedule_days, description, features) VALUES
('economy', '3 أيام', 'مكة المكرمة', 'يومياً', 'رحلة اقتصادية لأداء العمرة في مكة المكرمة مع توفير أفضل الخدمات بأسعار مناسبة', 
 JSON_ARRAY('إقامة قريبة من الحرم', 'وجبات يومية', 'نقل مكيف', 'مرشد ديني')),

('economy', '5 أيام', 'مكة المكرمة والمدينة المنورة', 'يومياً', 'رحلة اقتصادية شاملة تتضمن زيارة مكة المكرمة والمدينة المنورة', 
 JSON_ARRAY('إقامة في مكة والمدينة', 'زيارة المسجد النبوي', 'جولة في المعالم الدينية', 'وجبات يومية', 'نقل مكيف')),

('vip', '3 أيام', 'مكة المكرمة', 'الاثنين والخميس', 'رحلة VIP مميزة لأداء العمرة بأعلى مستوى من الخدمة والراحة', 
 JSON_ARRAY('فندق 5 نجوم قريب من الحرم', 'وجبات فاخرة', 'نقل VIP', 'خدمة شخصية', 'مرشد خاص'));

-- إدراج البيانات الأساسية لمعرض الصور
INSERT INTO gallery (image_path, alt_text, category, title, description, sort_order) VALUES
('assets/images/bus1.jpg', 'باص مكيف ومريح للرحلات', 'bus', 'باصات مكيفة ومريحة', 'باصات حديثة ومكيفة لضمان راحة الحجاج', 1),
('assets/images/bus2.jpg', 'باص VIP فاخر', 'bus', 'باصات VIP', 'باصات VIP فاخرة مع أعلى مستويات الراحة', 2),
('assets/images/hotel1.jpg', 'فندق قريب من الحرم المكي', 'hotel', 'فنادق قريبة من الحرم', 'فنادق مختارة بعناية قريبة من الحرم المكي الشريف', 1),
('assets/images/hotel2.jpg', 'غرف فندقية مريحة ونظيفة', 'hotel', 'غرف مريحة ونظيفة', 'غرف فندقية مجهزة بأحدث المرافق لراحة الضيوف', 2);

-- إدراج إعدادات الموقع الأساسية
INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'الزائرين', 'text', 'اسم الموقع'),
('site_description', 'رحلات العمرة من الرياض', 'text', 'وصف الموقع'),
('whatsapp_number', '966500000000', 'text', 'رقم الواتساب للتواصل'),
('contact_hours', 'السبت - الخميس: 8:00 ص - 10:00 م\nالجمعة: 2:00 م - 10:00 م', 'text', 'أوقات العمل'),
('hero_title', 'رحلات العمرة المميزة من الرياض', 'text', 'عنوان القسم الرئيسي'),
('hero_subtitle', 'نقدم لكم أفضل رحلات العمرة بأسعار مناسبة وخدمة متميزة', 'text', 'العنوان الفرعي للقسم الرئيسي'),
('about_title', 'عن الزائرين', 'text', 'عنوان قسم من نحن'),
('about_content', 'نحن في الزائرين نفخر بتقديم أفضل خدمات رحلات العمرة من الرياض. نسعى لجعل رحلتكم الروحانية تجربة لا تُنسى من خلال توفير أعلى مستويات الراحة والأمان.', 'text', 'محتوى قسم من نحن'),
('seo_keywords', 'عمرة, رحلات عمرة, الرياض, مكة, المدينة المنورة, رحلات اقتصادية, رحلات VIP', 'text', 'الكلمات المفتاحية للسيو'),
('google_analytics_id', '', 'text', 'معرف Google Analytics'),
('facebook_pixel_id', '', 'text', 'معرف Facebook Pixel');

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_trips_type ON trips(type);
CREATE INDEX idx_trips_active ON trips(is_active);
CREATE INDEX idx_gallery_category ON gallery(category);
CREATE INDEX idx_gallery_active ON gallery(is_active);
CREATE INDEX idx_gallery_sort ON gallery(sort_order);
CREATE INDEX idx_inquiries_status ON inquiries(status);
CREATE INDEX idx_inquiries_created ON inquiries(created_at);

-- إنشاء view لعرض الرحلات النشطة
CREATE VIEW active_trips AS
SELECT 
    id,
    type,
    duration,
    destination,
    schedule_days,
    description,
    features,
    CASE 
        WHEN type = 'economy' THEN 'رحلة اقتصادية'
        WHEN type = 'vip' THEN 'رحلة VIP'
    END as type_name,
    created_at
FROM trips 
WHERE is_active = TRUE 
ORDER BY type, duration;

-- إنشاء view لعرض الصور النشطة
CREATE VIEW active_gallery AS
SELECT 
    id,
    image_path,
    alt_text,
    category,
    title,
    description,
    sort_order,
    CASE 
        WHEN category = 'bus' THEN 'الباصات'
        WHEN category = 'hotel' THEN 'الفنادق'
        WHEN category = 'general' THEN 'عام'
    END as category_name,
    created_at
FROM gallery 
WHERE is_active = TRUE 
ORDER BY category, sort_order, id;

-- إنشاء stored procedure لجلب إعدادات الموقع
DELIMITER //
CREATE PROCEDURE GetSiteSettings()
BEGIN
    SELECT setting_key, setting_value, setting_type 
    FROM site_settings 
    ORDER BY setting_key;
END //
DELIMITER ;

-- إنشاء stored procedure لإضافة استفسار جديد
DELIMITER //
CREATE PROCEDURE AddInquiry(
    IN p_name VARCHAR(100),
    IN p_phone VARCHAR(20),
    IN p_trip_type ENUM('economy', 'vip'),
    IN p_message TEXT,
    IN p_source VARCHAR(50)
)
BEGIN
    INSERT INTO inquiries (name, phone, trip_type, message, source)
    VALUES (p_name, p_phone, p_trip_type, p_message, p_source);
    
    SELECT LAST_INSERT_ID() as inquiry_id;
END //
DELIMITER ;

-- إنشاء trigger لتحديث updated_at تلقائياً
DELIMITER //
CREATE TRIGGER tr_trips_updated_at 
    BEFORE UPDATE ON trips 
    FOR EACH ROW 
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END //

CREATE TRIGGER tr_gallery_updated_at 
    BEFORE UPDATE ON gallery 
    FOR EACH ROW 
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END //

CREATE TRIGGER tr_site_settings_updated_at 
    BEFORE UPDATE ON site_settings 
    FOR EACH ROW 
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END //
DELIMITER ;

-- إنشاء user للموقع (اختياري)
-- CREATE USER 'umrah_user'@'localhost' IDENTIFIED BY 'secure_password_here';
-- GRANT SELECT, INSERT, UPDATE ON umrah_trips.* TO 'umrah_user'@'localhost';
-- FLUSH PRIVILEGES;
