<?php
/**
 * ملف إعداد الموقع
 * Website Setup File
 */

// التحقق من إمكانية الوصول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])) {
    $setup_password = $_POST['setup_password'] ?? '';
    if ($setup_password !== 'alzz2024') {
        die('كلمة مرور خاطئة');
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد موقع الزائرين</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c5530 0%, #1a3d1f 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            color: #ffd700;
            margin-bottom: 30px;
        }
        .step {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #ffd700;
        }
        .success {
            background: rgba(40, 167, 69, 0.2);
            border-left-color: #28a745;
        }
        .error {
            background: rgba(220, 53, 69, 0.2);
            border-left-color: #dc3545;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-left-color: #ffc107;
        }
        button {
            background: #ffd700;
            color: #2c5530;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 5px;
        }
        button:hover {
            background: #ffed4e;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .code {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕌 إعداد موقع الزائرين</h1>
        
        <?php if (!isset($_POST['setup'])): ?>
        <form method="POST">
            <div class="step">
                <h3>🔐 التحقق من الهوية</h3>
                <p>أدخل كلمة المرور للمتابعة:</p>
                <input type="password" name="setup_password" placeholder="كلمة المرور" required>
                <button type="submit" name="setup">متابعة</button>
            </div>
        </form>
        <?php else: ?>
        
        <div class="step">
            <h3>✅ مرحباً بك في إعداد الموقع</h3>
            <p>سنقوم الآن بفحص النظام وإعداد الموقع تلقائياً.</p>
        </div>

        <?php
        // فحص PHP
        echo '<div class="step">';
        echo '<h3>🔍 فحص PHP</h3>';
        if (version_compare(PHP_VERSION, '7.4.0') >= 0) {
            echo '<p class="success">✅ إصدار PHP: ' . PHP_VERSION . ' (مناسب)</p>';
        } else {
            echo '<p class="error">❌ إصدار PHP: ' . PHP_VERSION . ' (يتطلب 7.4 أو أحدث)</p>';
        }
        
        // فحص الامتدادات المطلوبة
        $required_extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
        foreach ($required_extensions as $ext) {
            if (extension_loaded($ext)) {
                echo '<p class="success">✅ امتداد ' . $ext . ' متوفر</p>';
            } else {
                echo '<p class="error">❌ امتداد ' . $ext . ' غير متوفر</p>';
            }
        }
        echo '</div>';

        // فحص قاعدة البيانات
        echo '<div class="step">';
        echo '<h3>🗄️ فحص قاعدة البيانات</h3>';
        try {
            $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '');
            echo '<p class="success">✅ الاتصال بـ MySQL نجح</p>';
            
            // إنشاء قاعدة البيانات
            $pdo->exec("CREATE DATABASE IF NOT EXISTS umrah_trips CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo '<p class="success">✅ تم إنشاء قاعدة البيانات umrah_trips</p>';
            
            // الاتصال بقاعدة البيانات الجديدة
            $pdo = new PDO("mysql:host=localhost;dbname=umrah_trips;charset=utf8mb4", 'root', '');
            
            // تنفيذ ملف SQL
            if (file_exists('database.sql')) {
                $sql = file_get_contents('database.sql');
                $statements = array_filter(
                    array_map('trim', explode(';', $sql)),
                    function($statement) {
                        return !empty($statement) && 
                               !preg_match('/^(--|\/\*|\s*$)/', $statement) &&
                               !preg_match('/^(DELIMITER|CREATE USER|GRANT|FLUSH)/i', $statement);
                    }
                );
                
                foreach ($statements as $statement) {
                    try {
                        $pdo->exec($statement);
                    } catch (PDOException $e) {
                        if (strpos($e->getMessage(), 'already exists') === false) {
                            echo '<p class="warning">⚠️ تحذير: ' . $e->getMessage() . '</p>';
                        }
                    }
                }
                echo '<p class="success">✅ تم إنشاء الجداول بنجاح</p>';
            }
            
        } catch(PDOException $e) {
            echo '<p class="error">❌ خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';

        // فحص الملفات والمجلدات
        echo '<div class="step">';
        echo '<h3>📁 فحص الملفات</h3>';
        
        $required_files = [
            'index.php' => 'الصفحة الرئيسية',
            'config/database.php' => 'إعدادات قاعدة البيانات',
            'assets/css/style.css' => 'ملف التصميم',
            'assets/js/script.js' => 'ملف JavaScript',
            '.htaccess' => 'إعدادات Apache'
        ];
        
        foreach ($required_files as $file => $description) {
            if (file_exists($file)) {
                echo '<p class="success">✅ ' . $description . ' (' . $file . ')</p>';
            } else {
                echo '<p class="error">❌ ' . $description . ' (' . $file . ') غير موجود</p>';
            }
        }
        
        // فحص مجلد الصور
        if (is_dir('assets/images')) {
            echo '<p class="success">✅ مجلد الصور موجود</p>';
            $image_files = glob('assets/images/*.{jpg,jpeg,png,gif}', GLOB_BRACE);
            echo '<p>📸 عدد الصور الموجودة: ' . count($image_files) . '</p>';
        } else {
            echo '<p class="warning">⚠️ مجلد الصور غير موجود</p>';
        }
        echo '</div>';

        // إعدادات الموقع
        echo '<div class="step">';
        echo '<h3>⚙️ إعدادات الموقع</h3>';
        echo '<form method="POST" action="update_settings.php">';
        echo '<div class="form-group">';
        echo '<label>اسم الموقع:</label>';
        echo '<input type="text" name="site_name" value="الزائرين" required>';
        echo '</div>';
        
        echo '<div class="form-group">';
        echo '<label>وصف الموقع:</label>';
        echo '<input type="text" name="site_description" value="رحلات العمرة من الرياض" required>';
        echo '</div>';
        
        echo '<div class="form-group">';
        echo '<label>رقم الواتساب (بدون +):</label>';
        echo '<input type="text" name="whatsapp_number" value="966500000000" required>';
        echo '</div>';
        
        echo '<button type="submit">حفظ الإعدادات</button>';
        echo '</form>';
        echo '</div>';

        // تعليمات إضافية
        echo '<div class="step">';
        echo '<h3>📋 خطوات إضافية</h3>';
        echo '<ol>';
        echo '<li>ارفع صور الباصات والفنادق إلى مجلد <code>assets/images/</code></li>';
        echo '<li>تأكد من تحديث رقم الواتساب في الإعدادات</li>';
        echo '<li>احذف ملف <code>setup.php</code> بعد انتهاء الإعداد</li>';
        echo '<li>تأكد من أن ملف <code>.htaccess</code> يعمل بشكل صحيح</li>';
        echo '</ol>';
        echo '</div>';

        // روابط مفيدة
        echo '<div class="step">';
        echo '<h3>🔗 روابط مفيدة</h3>';
        echo '<p><a href="index.php" style="color: #ffd700;">🏠 الصفحة الرئيسية</a></p>';
        echo '<p><a href="test.php" style="color: #ffd700;">🧪 صفحة الاختبار</a></p>';
        echo '<p><a href="sitemap.php" style="color: #ffd700;">🗺️ خريطة الموقع</a></p>';
        echo '</div>';
        ?>
        
        <?php endif; ?>
    </div>
</body>
</html>
