<?php
/**
 * ملف الإعدادات العامة للموقع
 * General Configuration File
 */

// منع الوصول المباشر للملف
if (!defined('ALLOW_ACCESS')) {
    die('Access denied');
}

// إعدادات الموقع الأساسية
define('SITE_URL', 'https://' . $_SERVER['HTTP_HOST']);
define('SITE_PATH', dirname(dirname(__FILE__)));
define('ASSETS_URL', SITE_URL . '/assets');

// إعدادات الأمان
define('SECURE_KEY', 'your-secret-key-here-change-this');
define('ENABLE_CSRF_PROTECTION', true);
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات التطبيق
define('DEBUG_MODE', false);
define('LOG_ERRORS', true);
define('ERROR_LOG_FILE', SITE_PATH . '/logs/error.log');

// إعدادات قاعدة البيانات (يمكن نقلها من database.php)
define('DB_HOST', 'localhost');
define('DB_NAME', 'umrah_trips');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الواتساب
define('DEFAULT_WHATSAPP', '966500000000');
define('WHATSAPP_API_URL', 'https://wa.me/');

// إعدادات الصور
define('UPLOAD_PATH', SITE_PATH . '/assets/images/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'webp']);

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 3600); // ساعة واحدة
define('CACHE_PATH', SITE_PATH . '/cache/');

// إعدادات السيو
define('DEFAULT_META_DESCRIPTION', 'رحلات العمرة من الرياض - رحلات اقتصادية و VIP');
define('DEFAULT_META_KEYWORDS', 'عمرة, رحلات عمرة, الرياض, مكة, المدينة المنورة');
define('SITE_AUTHOR', 'الزائرين');

// إعدادات اللغة والمنطقة الزمنية
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DEFAULT_CURRENCY', 'SAR');

// إعدادات البريد الإلكتروني (للمستقبل)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '');
define('FROM_NAME', 'الزائرين');

// إعدادات وسائل التواصل الاجتماعي
define('FACEBOOK_URL', '');
define('TWITTER_URL', '');
define('INSTAGRAM_URL', '');
define('YOUTUBE_URL', '');

// إعدادات Google Analytics و Facebook Pixel
define('GOOGLE_ANALYTICS_ID', '');
define('FACEBOOK_PIXEL_ID', '');

// إعدادات الأداء
define('ENABLE_GZIP', true);
define('ENABLE_BROWSER_CACHE', true);
define('MINIFY_CSS', false);
define('MINIFY_JS', false);

// إعدادات الأمان المتقدمة
define('ENABLE_RATE_LIMITING', true);
define('MAX_REQUESTS_PER_MINUTE', 60);
define('BLOCK_SUSPICIOUS_IPS', true);

// إعدادات النسخ الاحتياطي
define('BACKUP_ENABLED', false);
define('BACKUP_PATH', SITE_PATH . '/backups/');
define('AUTO_BACKUP_INTERVAL', 86400); // يومياً

/**
 * كلاس مساعد للإعدادات
 */
class Config {
    private static $settings = [];
    
    /**
     * تحميل الإعدادات من قاعدة البيانات
     */
    public static function loadFromDatabase() {
        try {
            $db = Database::getInstance();
            $settings = $db->select("SELECT setting_key, setting_value FROM site_settings");
            
            foreach ($settings as $setting) {
                self::$settings[$setting['setting_key']] = $setting['setting_value'];
            }
        } catch (Exception $e) {
            error_log("خطأ في تحميل الإعدادات: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على قيمة إعداد
     */
    public static function get($key, $default = null) {
        return self::$settings[$key] ?? $default;
    }
    
    /**
     * تعيين قيمة إعداد
     */
    public static function set($key, $value) {
        self::$settings[$key] = $value;
    }
    
    /**
     * حفظ إعداد في قاعدة البيانات
     */
    public static function save($key, $value) {
        try {
            $db = Database::getInstance();
            $sql = "INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
            $db->update($sql, [$key, $value]);
            self::$settings[$key] = $value;
            return true;
        } catch (Exception $e) {
            error_log("خطأ في حفظ الإعداد: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public static function getAll() {
        return self::$settings;
    }
    
    /**
     * التحقق من وجود إعداد
     */
    public static function exists($key) {
        return isset(self::$settings[$key]);
    }
    
    /**
     * حذف إعداد
     */
    public static function delete($key) {
        try {
            $db = Database::getInstance();
            $db->delete("DELETE FROM site_settings WHERE setting_key = ?", [$key]);
            unset(self::$settings[$key]);
            return true;
        } catch (Exception $e) {
            error_log("خطأ في حذف الإعداد: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * دوال مساعدة عامة
 */

/**
 * تنظيف النص
 */
function sanitize_text($text) {
    return htmlspecialchars(strip_tags(trim($text)), ENT_QUOTES, 'UTF-8');
}

/**
 * تنسيق رقم الهاتف
 */
function format_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    if (substr($phone, 0, 1) === '0') {
        $phone = '966' . substr($phone, 1);
    }
    return $phone;
}

/**
 * إنشاء رابط واتساب
 */
function whatsapp_link($phone, $message = '') {
    $phone = format_phone($phone);
    $message = urlencode($message);
    return WHATSAPP_API_URL . $phone . ($message ? '?text=' . $message : '');
}

/**
 * تحويل التاريخ إلى التقويم الهجري (تقريبي)
 */
function to_hijri_date($date) {
    // تحويل تقريبي - يمكن استخدام مكتبة أكثر دقة
    $timestamp = is_string($date) ? strtotime($date) : $date;
    $hijri_year = date('Y', $timestamp) - 579;
    return $hijri_year . 'هـ';
}

/**
 * تنسيق العملة
 */
function format_currency($amount, $currency = DEFAULT_CURRENCY) {
    return number_format($amount, 2) . ' ' . $currency;
}

/**
 * إنشاء slug من النص العربي
 */
function create_slug($text) {
    $text = trim($text);
    $text = preg_replace('/\s+/', '-', $text);
    $text = preg_replace('/[^\p{Arabic}\p{N}\-]/u', '', $text);
    return strtolower($text);
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
function is_valid_saudi_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return preg_match('/^(966|0)?5[0-9]{8}$/', $phone);
}

/**
 * إنشاء token عشوائي
 */
function generate_token($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * تسجيل الأخطاء
 */
function log_error($message, $file = null) {
    if (LOG_ERRORS) {
        $log_file = $file ?: ERROR_LOG_FILE;
        $timestamp = date('Y-m-d H:i:s');
        $log_message = "[$timestamp] $message" . PHP_EOL;
        
        // إنشاء مجلد اللوجات إذا لم يكن موجوداً
        $log_dir = dirname($log_file);
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        
        file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
    }
}

/**
 * إعداد المنطقة الزمنية
 */
date_default_timezone_set(DEFAULT_TIMEZONE);

/**
 * إعداد معالج الأخطاء
 */
if (LOG_ERRORS) {
    set_error_handler(function($severity, $message, $file, $line) {
        log_error("Error: $message in $file on line $line");
    });
    
    set_exception_handler(function($exception) {
        log_error("Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
    });
}

// تحميل الإعدادات من قاعدة البيانات عند تضمين الملف
if (class_exists('Database')) {
    Config::loadFromDatabase();
}
?>
