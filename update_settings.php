<?php
/**
 * ملف تحديث إعدادات الموقع
 */

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: setup.php');
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=umrah_trips;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // تحديث الإعدادات
    $settings = [
        'site_name' => $_POST['site_name'] ?? 'الزائرين',
        'site_description' => $_POST['site_description'] ?? 'رحلات العمرة من الرياض',
        'whatsapp_number' => $_POST['whatsapp_number'] ?? '966500000000'
    ];
    
    foreach ($settings as $key => $value) {
        $sql = "INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?) 
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
        $pdo->prepare($sql)->execute([$key, $value]);
    }
    
    $success = true;
    $message = "تم حفظ الإعدادات بنجاح!";
    
} catch (Exception $e) {
    $success = false;
    $message = "خطأ في حفظ الإعدادات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث الإعدادات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c5530 0%, #1a3d1f 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 500px;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        .success {
            color: #28a745;
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .error {
            color: #dc3545;
            font-size: 3rem;
            margin-bottom: 20px;
        }
        h1 {
            color: #ffd700;
            margin-bottom: 20px;
        }
        button {
            background: #ffd700;
            color: #2c5530;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
        }
        button:hover {
            background: #ffed4e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="<?php echo $success ? 'success' : 'error'; ?>">
            <?php echo $success ? '✅' : '❌'; ?>
        </div>
        
        <h1><?php echo $success ? 'نجح التحديث!' : 'فشل التحديث!'; ?></h1>
        
        <p><?php echo htmlspecialchars($message); ?></p>
        
        <a href="setup.php" style="background: #ffd700; color: #2c5530; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; font-weight: bold; margin: 10px 5px; text-decoration: none; display: inline-block;">العودة للإعداد</a>
        
        <?php if ($success): ?>
        <a href="index.php" style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; font-weight: bold; margin: 10px 5px; text-decoration: none; display: inline-block;">عرض الموقع</a>
        <?php endif; ?>
    </div>
    
    <?php if ($success): ?>
    <script>
        // إعادة توجيه تلقائية بعد 3 ثوان
        setTimeout(function() {
            window.location.href = 'index.php';
        }, 3000);
    </script>
    <?php endif; ?>
</body>
</html>
