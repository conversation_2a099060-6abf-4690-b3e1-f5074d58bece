{"name": "alzz-umrah-trips", "version": "1.0.0", "description": "موقع رحلات العمرة من الرياض", "main": "index.php", "scripts": {"build": "npm run build:css && npm run build:js", "build:css": "postcss assets/css/style.css -o assets/css/style.min.css --use autoprefixer cssnano", "build:js": "uglifyjs assets/js/script.js -o assets/js/script.min.js -c -m", "watch": "npm run watch:css & npm run watch:js", "watch:css": "postcss assets/css/style.css -o assets/css/style.min.css --use autoprefixer cssnano --watch", "watch:js": "uglifyjs assets/js/script.js -o assets/js/script.min.js -c -m --watch", "serve": "php -S localhost:8000", "test": "echo \"No tests specified\" && exit 0", "lint": "eslint assets/js/", "format": "prettier --write assets/js/*.js assets/css/*.css"}, "keywords": ["umrah", "trips", "saudi", "riyadh", "mecca", "medina", "php", "mysql", "website"], "author": "الزائرين", "license": "MIT", "devDependencies": {"autoprefixer": "^10.4.0", "cssnano": "^5.1.0", "eslint": "^8.0.0", "postcss": "^8.4.0", "postcss-cli": "^9.1.0", "prettier": "^2.5.0", "uglify-js": "^3.15.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "repository": {"type": "git", "url": "https://github.com/alzz/umrah-trips.git"}, "bugs": {"url": "https://github.com/alzz/umrah-trips/issues"}, "homepage": "https://alzz.com"}