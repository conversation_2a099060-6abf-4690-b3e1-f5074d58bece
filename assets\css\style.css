/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #2c5530 0%, #1a3d1f 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.2rem;
    color: #ffd700;
}

.nav-brand span {
    font-size: 0.9rem;
    opacity: 0.9;
}

.whatsapp-btn {
    background: #25d366;
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.whatsapp-btn:hover {
    background: #128c7e;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
}

/* Hero Section */
.hero {
    height: 70vh;
    background: linear-gradient(rgba(44, 85, 48, 0.7), rgba(26, 61, 31, 0.8)), 
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23f0f8ff" width="1200" height="600"/><path fill="%23e6f3ff" d="M0,300 Q300,200 600,300 T1200,300 L1200,600 L0,600 Z"/></svg>');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
}

.hero-content h2 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.95;
}

.cta-btn {
    background: #ffd700;
    color: #2c5530;
    padding: 1rem 2rem;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.cta-btn:hover {
    background: #ffed4e;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

/* Trips Section */
.trips-section {
    padding: 5rem 0;
    background: white;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c5530;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: #ffd700;
    border-radius: 2px;
}

.trips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.trip-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.trip-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.trip-card.economy {
    border-color: #17a2b8;
}

.trip-card.vip {
    border-color: #ffd700;
    background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
}

.trip-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.trip-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c5530;
    margin-bottom: 0.5rem;
}

.duration {
    background: #2c5530;
    color: white;
    padding: 0.3rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.trip-details {
    margin-bottom: 2rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.8rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.detail-item i {
    color: #2c5530;
    font-size: 1.2rem;
    width: 20px;
}

.inquiry-btn {
    width: 100%;
    background: #25d366;
    color: white;
    padding: 1rem;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.inquiry-btn:hover {
    background: #128c7e;
    transform: translateY(-2px);
}

/* Gallery Section */
.gallery-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.gallery-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.tab-btn {
    background: white;
    border: 2px solid #2c5530;
    color: #2c5530;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: #2c5530;
    color: white;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(44, 85, 48, 0.9));
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

.gallery-overlay h4 {
    font-size: 1.2rem;
    font-weight: 600;
}

/* About Section */
.about-section {
    padding: 5rem 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c5530;
    margin-bottom: 1.5rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: #666;
}

.features {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #2c5530;
    font-weight: 600;
}

.feature i {
    font-size: 1.5rem;
    color: #ffd700;
}

.about-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

/* Contact Section */
.contact-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #2c5530 0%, #1a3d1f 100%);
    color: white;
    text-align: center;
}

.contact-section .section-title {
    color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 3rem;
}

.contact-info h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #ffd700;
}

.contact-info p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.whatsapp-contact {
    background: #25d366;
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.2rem;
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    transition: all 0.3s ease;
}

.whatsapp-contact:hover {
    background: #128c7e;
    transform: translateY(-3px);
}

.contact-hours h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffd700;
}

.contact-hours p {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

/* Footer */
.footer {
    background: #1a3d1f;
    color: white;
    padding: 2rem 0 1rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.footer-brand h3 {
    font-size: 1.5rem;
    color: #ffd700;
    margin-bottom: 0.5rem;
}

.footer-contact a {
    color: #25d366;
    font-size: 2rem;
    transition: color 0.3s ease;
}

.footer-contact a:hover {
    color: #128c7e;
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #2c5530;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h2 {
        font-size: 2rem;
    }
    
    .hero-content p {
        font-size: 1.1rem;
    }
    
    .trips-grid {
        grid-template-columns: 1fr;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .features {
        flex-direction: column;
        gap: 1rem;
    }
    
    .navbar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-content h2 {
        font-size: 1.8rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .trip-card {
        padding: 1.5rem;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
    }
}
