<?php
/**
 * إعدادات قاعدة البيانات
 * Database Configuration
 */

// إعدادات قاعدة البيانات الأساسية
define('DB_HOST', 'localhost');
define('DB_NAME', 'umrah_trips');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات PDO
define('PDO_OPTIONS', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
]);

/**
 * كلاس إدارة قاعدة البيانات
 * Database Management Class
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, PDO_OPTIONS);
        } catch (PDOException $e) {
            // محاولة إنشاء قاعدة البيانات إذا لم تكن موجودة
            try {
                $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
                $tempConnection = new PDO($dsn, DB_USER, DB_PASS, PDO_OPTIONS);
                $tempConnection->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET " . DB_CHARSET . " COLLATE utf8mb4_unicode_ci");
                
                // الاتصال بقاعدة البيانات الجديدة
                $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
                $this->connection = new PDO($dsn, DB_USER, DB_PASS, PDO_OPTIONS);
                
                // إنشاء الجداول
                $this->createTables();
                
            } catch (PDOException $e) {
                die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
            }
        }
    }
    
    /**
     * الحصول على instance وحيد من الكلاس
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * إنشاء الجداول الأساسية
     */
    private function createTables() {
        $sql = file_get_contents(__DIR__ . '/../database.sql');
        
        // تقسيم الاستعلامات
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($statement) {
                return !empty($statement) && 
                       !preg_match('/^(--|\/\*|\s*$)/', $statement) &&
                       !preg_match('/^(DELIMITER|CREATE USER|GRANT|FLUSH)/i', $statement);
            }
        );
        
        foreach ($statements as $statement) {
            try {
                $this->connection->exec($statement);
            } catch (PDOException $e) {
                // تجاهل الأخطاء المتعلقة بوجود الجداول مسبقاً
                if (strpos($e->getMessage(), 'already exists') === false) {
                    error_log("خطأ في إنشاء الجدول: " . $e->getMessage());
                }
            }
        }
    }
    
    /**
     * تنفيذ استعلام SELECT
     */
    public function select($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في استعلام SELECT: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $result = $stmt->execute($params);
            return $result ? $this->connection->lastInsertId() : false;
        } catch (PDOException $e) {
            error_log("خطأ في استعلام INSERT: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("خطأ في استعلام UPDATE: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("خطأ في استعلام DELETE: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * جلب سجل واحد
     */
    public function selectOne($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("خطأ في استعلام SELECT ONE: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * التحقق من وجود جدول
     */
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE :tableName";
        $result = $this->selectOne($sql, [':tableName' => $tableName]);
        return $result !== false;
    }
    
    /**
     * الحصول على عدد السجلات في جدول
     */
    public function getTableCount($tableName) {
        $sql = "SELECT COUNT(*) as count FROM `$tableName`";
        $result = $this->selectOne($sql);
        return $result ? $result['count'] : 0;
    }
    
    /**
     * تنظيف البيانات
     */
    public function sanitize($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * إغلاق الاتصال
     */
    public function close() {
        $this->connection = null;
    }
    
    /**
     * منع استنساخ الكلاس
     */
    private function __clone() {}
    
    /**
     * منع إلغاء التسلسل
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * كلاس مساعد للاستعلامات الشائعة
 * Helper class for common queries
 */
class QueryHelper {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * جلب جميع الرحلات النشطة
     */
    public function getActiveTrips() {
        $sql = "SELECT * FROM active_trips";
        return $this->db->select($sql);
    }
    
    /**
     * جلب الصور النشطة حسب الفئة
     */
    public function getActiveGallery($category = null) {
        if ($category) {
            $sql = "SELECT * FROM active_gallery WHERE category = :category";
            return $this->db->select($sql, [':category' => $category]);
        } else {
            $sql = "SELECT * FROM active_gallery";
            return $this->db->select($sql);
        }
    }
    
    /**
     * جلب إعدادات الموقع
     */
    public function getSiteSettings() {
        $sql = "SELECT setting_key, setting_value FROM site_settings";
        $results = $this->db->select($sql);
        
        $settings = [];
        foreach ($results as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        return $settings;
    }
    
    /**
     * جلب رحلة محددة
     */
    public function getTripById($id) {
        $sql = "SELECT * FROM trips WHERE id = :id AND is_active = 1";
        return $this->db->selectOne($sql, [':id' => $id]);
    }
    
    /**
     * إضافة استفسار جديد
     */
    public function addInquiry($name, $phone, $tripType, $message, $source = 'website') {
        $sql = "INSERT INTO inquiries (name, phone, trip_type, message, source) VALUES (:name, :phone, :trip_type, :message, :source)";
        return $this->db->insert($sql, [
            ':name' => $this->db->sanitize($name),
            ':phone' => $this->db->sanitize($phone),
            ':trip_type' => $tripType,
            ':message' => $this->db->sanitize($message),
            ':source' => $source
        ]);
    }
    
    /**
     * جلب إحصائيات بسيطة
     */
    public function getStats() {
        return [
            'total_trips' => $this->db->getTableCount('trips'),
            'total_gallery' => $this->db->getTableCount('gallery'),
            'total_inquiries' => $this->db->getTableCount('inquiries')
        ];
    }
}

// إنشاء اتصال عالمي (اختياري)
function getDB() {
    return Database::getInstance();
}

function getQueryHelper() {
    return new QueryHelper();
}
?>
